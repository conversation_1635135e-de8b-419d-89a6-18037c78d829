# 场景生成提示模板

## 基础模板

### 场景设定提示
```
创建一个[时间]在[地点]发生的场景，主要角色是[角色名]。

核心冲突：[具体冲突描述]

场景目标：
- 展现[具体制度/概念]的运作方式
- 发展[角色名]的[具体特征]
- 推进[情节要素]

语调要求：
- 使用"正能量表达"标准表达
- 保持"直面荒诞的严肃"风格
- 避免直接批判，展现系统逻辑

技术要求：
- 包含AI系统反馈
- 使用标准术语（参考术语词汇表）
- 长度：800-1200字
```

### 对话场景模板
```
在[场景设定]中，[角色A]与[角色B]进行关于[话题]的对话。

对话要求：
- [角色A]的立场：[立场描述]
- [角色B]的立场：[立场描述]
- 核心分歧：[分歧点]

语言特点：
- 大量使用制度术语
- 体现角色适应程度差异
- 展现内心与表达的分离

结果：[期望的对话结果]
```

## 专用模板

### AI评估场景
```
描述[角色]接受[具体评估类型]的过程：

评估内容：
- 测试项目：[具体项目]
- 评分标准：[标准描述]
- 角色反应：[预期反应]

AI反馈格式：
🧠 RESTORE AI 反馈: [具体评估结果]

冲突点：[人性与算法的冲突]
```

### 制度解释场景
```
通过[情境]向读者解释[制度名称]：

制度特点：
- 表面合理性：[合理之处]
- 实际影响：[对个人的影响]
- 内在矛盾：[潜在问题]

解释方式：
- 通过角色体验展现，非直接说教
- 展现制度的日常化应用
- 突出看似合理的荒诞性
``` 