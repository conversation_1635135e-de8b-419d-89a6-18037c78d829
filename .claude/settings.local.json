{"permissions": {"allow": ["<PERSON><PERSON>(touch:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(ls:*)", "Bash(gh auth:*)", "Bash(gh repo create:*)", "Bash(git remote add:*)", "Bash(git branch:*)", "Bash(git push:*)", "Bash(gh repo view:*)", "Bash(gh api:*)", "<PERSON><PERSON>(sed:*)", "Bash(grep:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(env)", "<PERSON><PERSON>(mcp:*)", "Bash(find:*)", "Bash(npm install:*)", "Bash(npx *)", "Bash(npm ping)", "Bash(npm config get:*)", "Bash(timeout 30s npx --yes @modelcontextprotocol/server-filesystem --version)", "Bash(npm config set:*)"], "deny": []}}