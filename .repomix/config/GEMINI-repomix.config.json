{"output": {"filePath": "/Users/<USER>/Documents/papers/republic-of-consensus/", "style": "markdown", "parsableStyle": false, "headerText": "", "instructionFilePath": "INSTRUCT.md", "fileSummary": true, "directoryStructure": true, "removeComments": false, "removeEmptyLines": true, "topFilesLength": 5, "showLineNumbers": false, "copyToClipboard": false, "includeEmptyDirectories": false, "compress": false}, "include": [], "ignore": {"useGitignore": true, "useDefaultPatterns": true, "customPatterns": []}, "security": {"enableSecurityCheck": true}, "tokenCount": {"encoding": "o200k_base"}, "cwd": "/Users/<USER>/Documents/papers/republic-of-consensus"}